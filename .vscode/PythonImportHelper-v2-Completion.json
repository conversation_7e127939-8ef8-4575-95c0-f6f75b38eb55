[{"label": "Dataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "esm", "kind": 6, "isExtraImport": true, "importPath": "esm", "description": "esm", "detail": "esm", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "amp", "importPath": "torch.cuda", "description": "torch.cuda", "isExtraImport": true, "detail": "torch.cuda", "documentation": {}}, {"label": "amp", "importPath": "torch.cuda", "description": "torch.cuda", "isExtraImport": true, "detail": "torch.cuda", "documentation": {}}, {"label": "stats", "importPath": "scipy", "description": "scipy", "isExtraImport": true, "detail": "scipy", "documentation": {}}, {"label": "stats", "importPath": "scipy", "description": "scipy", "isExtraImport": true, "detail": "scipy", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "random", "kind": 6, "isExtraImport": true, "importPath": "random", "description": "random", "detail": "random", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "make_model", "importPath": "model", "description": "model", "isExtraImport": true, "detail": "model", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "set_seed", "importPath": "utils.set_seed", "description": "utils.set_seed", "isExtraImport": true, "detail": "utils.set_seed", "documentation": {}}, {"label": "warnings", "kind": 6, "isExtraImport": true, "importPath": "warnings", "description": "warnings", "detail": "warnings", "documentation": {}}, {"label": "make_dataloader", "importPath": "datasets", "description": "datasets", "isExtraImport": true, "detail": "datasets", "documentation": {}}, {"label": "make_dataloader", "importPath": "datasets", "description": "datasets", "isExtraImport": true, "detail": "datasets", "documentation": {}}, {"label": "make_dataloader", "importPath": "datasets", "description": "datasets", "isExtraImport": true, "detail": "datasets", "documentation": {}}, {"label": "make_dataloader", "importPath": "datasets", "description": "datasets", "isExtraImport": true, "detail": "datasets", "documentation": {}}, {"label": "make_dataloader", "importPath": "datasets", "description": "datasets", "isExtraImport": true, "detail": "datasets", "documentation": {}}, {"label": "do_test", "importPath": "processor", "description": "processor", "isExtraImport": true, "detail": "processor", "documentation": {}}, {"label": "do_test", "importPath": "processor", "description": "processor", "isExtraImport": true, "detail": "processor", "documentation": {}}, {"label": "do_train", "importPath": "processor", "description": "processor", "isExtraImport": true, "detail": "processor", "documentation": {}}, {"label": "make_optimizer", "importPath": "solver", "description": "solver", "isExtraImport": true, "detail": "solver", "documentation": {}}, {"label": "make_scheduler", "importPath": "solver", "description": "solver", "isExtraImport": true, "detail": "solver", "documentation": {}}, {"label": "make_optimizer", "importPath": "solver", "description": "solver", "isExtraImport": true, "detail": "solver", "documentation": {}}, {"label": "make_scheduler", "importPath": "solver", "description": "solver", "isExtraImport": true, "detail": "solver", "documentation": {}}, {"label": "ProteinDataset", "kind": 6, "importPath": "datasets.ddg_dataset", "description": "datasets.ddg_dataset", "peekOfCode": "class ProteinDataset(Dataset):\n    def __init__(self, df):\n        self.df = df\n        _, esm1v_alphabet = esm.pretrained.esm2_t33_650M_UR50D()\n        self.esm1v_batch_converter = esm1v_alphabet.get_batch_converter()\n    def __getitem__(self, idx):\n        _, _, esm1b_batch_tokens1 = self.esm1v_batch_converter([('' , ''.join(self.df.iloc[idx]['wt_seq'])[:1022])])\n        _, _, esm1b_batch_tokens2 = self.esm1v_batch_converter([('' , ''.join(self.df.iloc[idx]['mut_seq'])[:1022])])\n        pos = self.df.iloc[idx]['pos']\n        return esm1b_batch_tokens1.squeeze(dim=0), esm1b_batch_tokens2.squeeze(dim=0), pos+1, torch.FloatTensor([self.df.iloc[idx]['ddg']])", "detail": "datasets.ddg_dataset", "documentation": {}}, {"label": "DdgData", "kind": 6, "importPath": "datasets.ddg_dataset", "description": "datasets.ddg_dataset", "peekOfCode": "class DdgData(object):\n    def __init__(self, data_path):\n        _, esm2_alphabet = esm.pretrained.esm2_t33_650M_UR50D()\n        self.tokenizer = esm2_alphabet.get_batch_converter()\n        dataframe = pd.read_csv(data_path)\n        self.wild_tokens, self.mutant_tokens, self.positions, self.ddGs, self.cls = self.__process__(dataframe)\n    def __process__(self, data):\n        data_list = data_frame_to_list(data)\n        wild_seqs = []\n        mutation_seqs = []", "detail": "datasets.ddg_dataset", "documentation": {}}, {"label": "ddG_Dataset", "kind": 6, "importPath": "datasets.ddg_dataset", "description": "datasets.ddg_dataset", "peekOfCode": "class ddG_Dataset(Dataset):\n    def __init__(self, wild_tokens, mutant_tokens, positions, ddGs, cls):\n        self.wild_tokens = wild_tokens\n        self.mutant_tokens = mutant_tokens\n        self.positions = positions\n        self.ddGs = ddGs\n        self.cls = cls\n    def __getitem__(self, idx):\n        return (\n            self.wild_tokens[idx],", "detail": "datasets.ddg_dataset", "documentation": {}}, {"label": "data_frame_to_list", "kind": 2, "importPath": "datasets.ddg_dataset", "description": "datasets.ddg_dataset", "peekOfCode": "def data_frame_to_list(dataframe):\n    header = [\"wt_seq\", \"mut_seq\", \"ddg\", \"pos\"]\n    lenOfDate = len(dataframe)\n    listOfdate = []\n    for raw in range(lenOfDate):\n        listTemp = []\n        for name in header:\n            listTemp.append(dataframe.iloc[raw][name])\n        listOfdate.append(listTemp)\n    return listOfdate", "detail": "datasets.ddg_dataset", "documentation": {}}, {"label": "make_dataloader", "kind": 2, "importPath": "datasets.make_dataloader", "description": "datasets.make_dataloader", "peekOfCode": "def make_dataloader(train_dataset_path=\"\", test_dataset_path=\"\", batch_size=8, is_training=True):\n    if is_training:\n        train_data, test_data = DdgData(train_dataset_path), DdgData(test_dataset_path)\n        train_ds = ddG_Dataset(train_data.wild_tokens, train_data.mutant_tokens, train_data.positions, train_data.ddGs, train_data.cls)\n        test_ds = ddG_Dataset(test_data.wild_tokens, test_data.mutant_tokens, test_data.positions, test_data.ddGs, test_data.cls)\n        training_loader = DataLoader(train_ds, batch_size=batch_size, num_workers = 8, shuffle = True)\n        testing_loader = DataLoader(test_ds, batch_size=batch_size, num_workers = 8)\n        return training_loader, testing_loader\n    else:\n        test_data = DdgData(test_dataset_path)", "detail": "datasets.make_dataloader", "documentation": {}}, {"label": "make_model", "kind": 2, "importPath": "model.make_model", "description": "model.make_model", "peekOfCode": "def make_model(model_name):\n    model_class = globals()[model_name]\n    model = model_class()\n    return model", "detail": "model.make_model", "documentation": {}}, {"label": "Env", "kind": 6, "importPath": "model.models", "description": "model.models", "peekOfCode": "class Env(nn.<PERSON><PERSON><PERSON>):\n    # mutual attention weight selection\n    def __init__(self):\n        super(Env, self).__init__()\n    def forward(self, x):\n        # --> x[B, H, T] --> [B, T]\n        weights = x.mean(1)\n        max_inx = torch.argsort(weights, dim=1, descending=True)\n        return max_inx  \nclass ESM_ensamble_cls_env_attention(nn.Module):", "detail": "model.models", "documentation": {}}, {"label": "ESM_ensamble_cls_env_attention", "kind": 6, "importPath": "model.models", "description": "model.models", "peekOfCode": "class ESM_ensamble_cls_env_attention(nn.Module):\n    def __init__(self):\n        super().__init__() \n        self.esm2, self.esm2_alphabet = esm.pretrained.esm2_t33_650M_UR50D()        \n        self.cls_transform = nn.Linear(1280, 1280)\n        self.env_transform = nn.Linear(1280, 1280)\n        self.cls_classifier = nn.Linear(1280, 1)\n        self.cls_const1 = torch.nn.Parameter(torch.ones((1,1280)))\n        self.cls_const2 = torch.nn.Parameter(-1 * torch.ones((1,1280)))\n        self.token_select = Env()", "detail": "model.models", "documentation": {}}, {"label": "DropPath", "kind": 6, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "class DropPath(nn.Module):\n    \"\"\"Drop paths (Stochastic Depth) per sample  (when applied in main path of residual blocks).\n    \"\"\"\n    def __init__(self, drop_prob=None):\n        super(DropPath, self).__init__()\n        self.drop_prob = drop_prob\n    def forward(self, x):\n        return drop_path(x, self.drop_prob, self.training)\nclass Mlp(nn.Module):\n    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):", "detail": "model.utils", "documentation": {}}, {"label": "Mlp", "kind": 6, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "class Mlp(nn.Module):\n    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):\n        super().__init__()\n        out_features = out_features or in_features\n        hidden_features = hidden_features or in_features\n        self.fc1 = nn.Linear(in_features, hidden_features)\n        self.act = act_layer()\n        self.fc2 = nn.Linear(hidden_features, out_features)\n        self.drop = nn.Dropout(drop)\n    def forward(self, x):", "detail": "model.utils", "documentation": {}}, {"label": "Attention", "kind": 6, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "class Attention(nn.Module):\n    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0.):\n        super().__init__()\n        self.num_heads = num_heads\n        head_dim = dim // num_heads\n        # NOTE scale factor was wrong in my original version, can set manually to be compat with prev weights\n        self.scale = qk_scale or head_dim ** -0.5\n        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)\n        self.attn_drop = nn.Dropout(attn_drop)\n        self.proj = nn.Linear(dim, dim)", "detail": "model.utils", "documentation": {}}, {"label": "Block", "kind": 6, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "class Block(nn.Module):\n    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,\n                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):\n        super().__init__()\n        self.norm1 = norm_layer(dim)\n        self.attn = Attention(\n            dim, num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)\n        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here\n        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()\n        self.norm2 = norm_layer(dim)", "detail": "model.utils", "documentation": {}}, {"label": "drop_path", "kind": 2, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "def drop_path(x, drop_prob: float = 0., training: bool = False):\n    \"\"\"Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks).\n    This is the same as the DropConnect impl I created for EfficientNet, etc networks, however,\n    the original name is misleading as 'Drop Connect' is a different form of dropout in a separate paper...\n    See discussion: https://github.com/tensorflow/tpu/issues/494#issuecomment-532968956 ... I've opted for\n    changing the layer and argument names to 'drop path' rather than mix DropConnect as a layer name and use\n    'survival rate' as the argument.\n    \"\"\"\n    if drop_prob == 0. or not training:\n        return x", "detail": "model.utils", "documentation": {}}, {"label": "trunc_normal_", "kind": 2, "importPath": "model.utils", "description": "model.utils", "peekOfCode": "def trunc_normal_(tensor, mean=0., std=1., a=-2., b=2.):\n    # type: (Tensor, float, float, float, float) -> Tensor\n    r\"\"\"Fills the input Tensor with values drawn from a truncated\n    normal distribution. The values are effectively drawn from the\n    normal distribution :math:`\\mathcal{N}(\\text{mean}, \\text{std}^2)`\n    with values outside :math:`[a, b]` redrawn until they are within\n    the bounds. The method used for generating the random values works\n    best when :math:`a \\leq \\text{mean} \\leq b`.\n    Args:\n        tensor: an n-dimensional `torch.Tensor`", "detail": "model.utils", "documentation": {}}, {"label": "do_test", "kind": 2, "importPath": "processor.do_test", "description": "processor.do_test", "peekOfCode": "def do_test(model, testing_loader, device):\n    model.to(device)\n    labels, predictions = do_valid(model, testing_loader, device)\n    print(f'RMSE {np.sqrt(np.mean((labels-predictions)**2))} MAE {np.mean(np.abs(labels - predictions))} Correlation {stats.spearmanr(labels, predictions)}, Pearsonr {pearsonr(labels, predictions)[0]}')\ndef do_valid(model, testing_loader, device):\n    model.eval()\n    eval_loss, eval_accuracy = 0, 0\n    nb_eval_examples, nb_eval_steps = 0, 0\n    eval_preds, eval_labels, eval_scores = [], [], []\n    with torch.no_grad():", "detail": "processor.do_test", "documentation": {}}, {"label": "do_valid", "kind": 2, "importPath": "processor.do_test", "description": "processor.do_test", "peekOfCode": "def do_valid(model, testing_loader, device):\n    model.eval()\n    eval_loss, eval_accuracy = 0, 0\n    nb_eval_examples, nb_eval_steps = 0, 0\n    eval_preds, eval_labels, eval_scores = [], [], []\n    with torch.no_grad():\n        for idx, batch in enumerate(testing_loader):\n            input_ids1, input_ids2, pos, labels, classes = batch            \n            input_ids1 = input_ids1.to(device)\n            input_ids2 = input_ids2.to(device)", "detail": "processor.do_test", "documentation": {}}, {"label": "do_train", "kind": 2, "importPath": "processor.do_train", "description": "processor.do_train", "peekOfCode": "def do_train(model, optimizer, scheduler, training_loader, testing_loader, device, epochs):\n    scaler = amp.GradScaler()\n    for epoch in range(epochs):\n        model.train()\n        tr_loss = 0.0\n        nb_tr_examples, nb_tr_steps = 0, 0\n        for idx, batch in enumerate(training_loader):\n            optimizer.zero_grad()\n            input_ids1, input_ids2, pos, labels, classes = batch\n            input_ids1 = input_ids1.to(device)", "detail": "processor.do_train", "documentation": {}}, {"label": "do_valid", "kind": 2, "importPath": "processor.do_train", "description": "processor.do_train", "peekOfCode": "def do_valid(model, testing_loader, device):\n    model.eval()\n    eval_loss, eval_accuracy = 0, 0\n    nb_eval_examples, nb_eval_steps = 0, 0\n    eval_preds, eval_labels, eval_scores = [], [], []\n    with torch.no_grad():\n        for idx, batch in enumerate(testing_loader):\n            input_ids1, input_ids2, pos, labels, classes = batch  \n            # input_ids1, input_ids2, pos, labels = batch          \n            input_ids1 = input_ids1.to(device)", "detail": "processor.do_train", "documentation": {}}, {"label": "CosineLRScheduler", "kind": 6, "importPath": "solver.cosine_lr", "description": "solver.cosine_lr", "peekOfCode": "class CosineLRScheduler(Scheduler):\n    \"\"\"\n    Cosine decay with restarts.\n    This is described in the paper https://arxiv.org/abs/1608.03983.\n    Inspiration from\n    https://github.com/allenai/allennlp/blob/master/allennlp/training/learning_rate_schedulers/cosine.py\n    \"\"\"\n    def __init__(self,\n                 optimizer: torch.optim.Optimizer,\n                 t_initial: int,", "detail": "solver.cosine_lr", "documentation": {}}, {"label": "_logger", "kind": 5, "importPath": "solver.cosine_lr", "description": "solver.cosine_lr", "peekOfCode": "_logger = logging.getLogger(__name__)\nclass CosineLRScheduler(Scheduler):\n    \"\"\"\n    Cosine decay with restarts.\n    This is described in the paper https://arxiv.org/abs/1608.03983.\n    Inspiration from\n    https://github.com/allenai/allennlp/blob/master/allennlp/training/learning_rate_schedulers/cosine.py\n    \"\"\"\n    def __init__(self,\n                 optimizer: torch.optim.Optimizer,", "detail": "solver.cosine_lr", "documentation": {}}, {"label": "make_optimizer", "kind": 2, "importPath": "solver.make_optimizer", "description": "solver.make_optimizer", "peekOfCode": "def make_optimizer(model, lr, optimizer_name=\"<PERSON>\"):\n    if optimizer_name == \"Adam\":\n        print(\"using <PERSON>\")\n        optimizer = torch.optim.Adam(params=model.parameters(), lr=lr)\n    elif optimizer_name == \"AdamW\":\n        print(\"using AdamW\")\n        optimizer = torch.optim.AdamW(params=model.parameters(), lr=lr, weight_decay=0.01)\n    return optimizer", "detail": "solver.make_optimizer", "documentation": {}}, {"label": "make_scheduler", "kind": 2, "importPath": "solver.make_scheduler", "description": "solver.make_scheduler", "peekOfCode": "def make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name, warm_up=5):\n    if scheduler_name == \"OneCycleLR\":\n        print(\"using OneCycleLR\")\n        scheduler = torch.optim.lr_scheduler.OneCycleLR(optimizer, max_lr=lr, steps_per_epoch=len(training_loader), epochs=epochs)\n    elif scheduler_name == \"ExponentialLR\":\n        print(\"using ExponentialLR\")\n        scheduler = torch.optim.lr_scheduler.ExponentialLR(optimizer, gamma=0.1, last_epoch=-1, verbose=False)\n    elif scheduler_name == \"CosineLR\":\n        print(\"using CosineLR\")\n        num_epochs = epochs", "detail": "solver.make_scheduler", "documentation": {}}, {"label": "Scheduler", "kind": 6, "importPath": "solver.scheduler", "description": "solver.scheduler", "peekOfCode": "class Scheduler:\n    \"\"\" Parameter Scheduler Base Class\n    A scheduler base class that can be used to schedule any optimizer parameter groups.\n    Unlike the builtin PyTorch schedulers, this is intended to be consistently called\n    * At the END of each epoch, before incrementing the epoch count, to calculate next epoch's value\n    * At the END of each optimizer update, after incrementing the update count, to calculate next update's value\n    The schedulers built on this should try to remain as stateless as possible (for simplicity).\n    This family of schedulers is attempting to avoid the confusion of the meaning of 'last_epoch'\n    and -1 values for special behaviour. All epoch and update counts must be tracked in the training\n    code and explicitly passed in to the schedulers on the corresponding step or step_update call.", "detail": "solver.scheduler", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "utils.logger", "description": "utils.logger", "peekOfCode": "def setup_logger(name, save_path=\"logs\", log_name=\"train_log.txt\", if_train=True):\n    \"\"\"\n        using in main py script, such as \"logger = setup_logger(\"train\", output_dir, if_train=True)\"\n        in other scripy, using logger = logging.getLogger(\"train\")\n    \"\"\"\n    logger = logging.getLogger(name)\n    logger.setLevel(logging.DEBUG)\n    ch = logging.StreamHandler(stream=sys.stdout)\n    ch.setLevel(logging.DEBUG)\n    formatter = logging.Formatter(\"%(asctime)s %(name)s %(levelname)s: %(message)s\")", "detail": "utils.logger", "documentation": {}}, {"label": "AverageMeter", "kind": 6, "importPath": "utils.meter", "description": "utils.meter", "peekOfCode": "class AverageMeter(object):\n    \"\"\"Computes and stores the average and current value\"\"\"\n    def __init__(self):\n        self.val = 0\n        self.avg = 0\n        self.sum = 0\n        self.count = 0\n    def reset(self):\n        self.val = 0\n        self.avg = 0", "detail": "utils.meter", "documentation": {}}, {"label": "set_seed", "kind": 2, "importPath": "utils.set_seed", "description": "utils.set_seed", "peekOfCode": "def set_seed(seed):\n    torch.manual_seed(seed)\n    torch.cuda.manual_seed(seed)\n    torch.cuda.manual_seed_all(seed)\n    np.random.seed(seed)\n    random.seed(seed)\n    torch.backends.cudnn.deterministic = True\n    torch.backends.cudnn.benchmark = True", "detail": "utils.set_seed", "documentation": {}}, {"label": "MutationPredictor", "kind": 6, "importPath": "predict_mutation", "description": "predict_mutation", "peekOfCode": "class MutationPredictor:\n    def __init__(self, checkpoint_path=None, device=\"cuda:0\"):\n        \"\"\"\n        初始化突变预测器\n        Args:\n            checkpoint_path: 预训练模型路径，如果为None则使用随机初始化模型\n            device: 计算设备\n        \"\"\"\n        self.device = device if torch.cuda.is_available() else \"cpu\"\n        print(f\"Using device: {self.device}\")", "detail": "predict_mutation", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "predict_mutation", "description": "predict_mutation", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(description='DVE-stability Mutation Prediction')\n    parser.add_argument('--wild_seq', type=str, help='Wild-type protein sequence')\n    parser.add_argument('--mutation', type=str, help='Mutation in format \"D23A\"')\n    parser.add_argument('--position', type=int, help='Mutation position (0-based)')\n    parser.add_argument('--input_file', type=str, help='CSV file with mutations to predict')\n    parser.add_argument('--output_file', type=str, help='Output CSV file for batch predictions')\n    parser.add_argument('--checkpoint', type=str, help='Path to trained model checkpoint')\n    parser.add_argument('--device', type=str, default='cuda:0', help='Device to use')\n    args = parser.parse_args()", "detail": "predict_mutation", "documentation": {}}, {"label": "quick_predict", "kind": 2, "importPath": "quick_predict", "description": "quick_predict", "peekOfCode": "def quick_predict():\n    \"\"\"快速预测函数\"\"\"\n    print(\"=\" * 60)\n    print(\"DVE-stability 快速突变预测\")\n    print(\"=\" * 60)\n    # 设置设备\n    device = \"cuda:0\" if torch.cuda.is_available() else \"cpu\"\n    print(f\"使用设备: {device}\")\n    # 初始化模型\n    print(\"正在加载模型...\")", "detail": "quick_predict", "documentation": {}}, {"label": "demo_mode", "kind": 2, "importPath": "quick_predict", "description": "quick_predict", "peekOfCode": "def demo_mode():\n    \"\"\"演示模式\"\"\"\n    print(\"=\" * 60)\n    print(\"DVE-stability 演示模式\")\n    print(\"=\" * 60)\n    # 示例数据\n    examples = [\n        {\n            \"name\": \"示例1: 稳定化突变\",\n            \"seq\": \"MKVIFLKDVKGKGKKGEIKNVADGYANNFLFKQGLAIEATPANLKALEAQKQKEQRQAAEELANAKKLKEQLEKLTVTIPAKAGEGGRLFGSITSKQIAESLQAQHGLKLDKRKIELADAIRALGYTNVPVKLHPEVTATLKVHVTEQK\",", "detail": "quick_predict", "documentation": {}}, {"label": "batch_size", "kind": 5, "importPath": "test", "description": "test", "peekOfCode": "batch_size = 8\ndevice = \"cuda:0\"\nset_seed(1024)\ntesting_loader = make_dataloader(test_dataset_path = \"data/s669.csv\", batch_size=batch_size, is_training=False)\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\ncheckpoint = torch.load(\"[path of your checkpoint]\", map_location=\"cpu\")\nmodel.load_state_dict(checkpoint)\ndo_test(\n    model,\n    testing_loader,", "detail": "test", "documentation": {}}, {"label": "device", "kind": 5, "importPath": "test", "description": "test", "peekOfCode": "device = \"cuda:0\"\nset_seed(1024)\ntesting_loader = make_dataloader(test_dataset_path = \"data/s669.csv\", batch_size=batch_size, is_training=False)\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\ncheckpoint = torch.load(\"[path of your checkpoint]\", map_location=\"cpu\")\nmodel.load_state_dict(checkpoint)\ndo_test(\n    model,\n    testing_loader,\n    device", "detail": "test", "documentation": {}}, {"label": "testing_loader", "kind": 5, "importPath": "test", "description": "test", "peekOfCode": "testing_loader = make_dataloader(test_dataset_path = \"data/s669.csv\", batch_size=batch_size, is_training=False)\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\ncheckpoint = torch.load(\"[path of your checkpoint]\", map_location=\"cpu\")\nmodel.load_state_dict(checkpoint)\ndo_test(\n    model,\n    testing_loader,\n    device\n)", "detail": "test", "documentation": {}}, {"label": "model", "kind": 5, "importPath": "test", "description": "test", "peekOfCode": "model = make_model(\"ESM_ensamble_cls_env_attention\")\ncheckpoint = torch.load(\"[path of your checkpoint]\", map_location=\"cpu\")\nmodel.load_state_dict(checkpoint)\ndo_test(\n    model,\n    testing_loader,\n    device\n)", "detail": "test", "documentation": {}}, {"label": "checkpoint", "kind": 5, "importPath": "test", "description": "test", "peekOfCode": "checkpoint = torch.load(\"[path of your checkpoint]\", map_location=\"cpu\")\nmodel.load_state_dict(checkpoint)\ndo_test(\n    model,\n    testing_loader,\n    device\n)", "detail": "test", "documentation": {}}, {"label": "test_imports", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def test_imports():\n    \"\"\"测试所有必要的包是否可以正常导入\"\"\"\n    print(\"Testing imports...\")\n    try:\n        import esm\n        print(\"✓ ESM imported successfully\")\n        import matplotlib.pyplot as plt\n        print(\"✓ Matplotlib imported successfully\")\n        import sklearn\n        print(\"✓ Scikit-learn imported successfully\")", "detail": "test_environment", "documentation": {}}, {"label": "test_data_loading", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def test_data_loading():\n    \"\"\"测试数据加载功能\"\"\"\n    print(\"\\nTesting data loading...\")\n    try:\n        # 检查数据文件是否存在\n        import os\n        if not os.path.exists(\"data/s669.csv\"):\n            print(\"✗ Test data file not found: data/s669.csv\")\n            return False\n        # 尝试加载数据", "detail": "test_environment", "documentation": {}}, {"label": "test_model_creation", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def test_model_creation():\n    \"\"\"测试模型创建\"\"\"\n    print(\"\\nTesting model creation...\")\n    try:\n        set_seed(1024)\n        model = make_model(\"ESM_ensamble_cls_env_attention\")\n        print(\"✓ Model created successfully\")\n        # 检查模型参数\n        total_params = sum(p.numel() for p in model.parameters())\n        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)", "detail": "test_environment", "documentation": {}}, {"label": "test_model_forward", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def test_model_forward():\n    \"\"\"测试模型前向传播（使用虚拟数据）\"\"\"\n    print(\"\\nTesting model forward pass...\")\n    try:\n        set_seed(1024)\n        model = make_model(\"ESM_ensamble_cls_env_attention\")\n        model.eval()\n        # 创建虚拟输入数据\n        batch_size = 2\n        seq_length = 50", "detail": "test_environment", "documentation": {}}, {"label": "test_cuda_availability", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def test_cuda_availability():\n    \"\"\"测试CUDA是否可用\"\"\"\n    print(\"\\nTesting CUDA availability...\")\n    if torch.cuda.is_available():\n        print(f\"✓ CUDA is available\")\n        print(f\"✓ CUDA device count: {torch.cuda.device_count()}\")\n        print(f\"✓ Current CUDA device: {torch.cuda.current_device()}\")\n        print(f\"✓ CUDA device name: {torch.cuda.get_device_name()}\")\n        return True\n    else:", "detail": "test_environment", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_environment", "description": "test_environment", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"=\" * 60)\n    print(\"DVE-stability Environment Test\")\n    print(\"=\" * 60)\n    # 运行所有测试\n    tests_passed = 0\n    total_tests = 5\n    if test_imports():\n        tests_passed += 1", "detail": "test_environment", "documentation": {}}, {"label": "test_inference", "kind": 2, "importPath": "test_inference", "description": "test_inference", "peekOfCode": "def test_inference():\n    \"\"\"测试推理功能\"\"\"\n    print(\"Testing inference...\")\n    # 设置参数\n    batch_size = 1  # 减小batch size以节省GPU内存\n    device = \"cuda:0\" if torch.cuda.is_available() else \"cpu\"\n    # 清理GPU内存\n    if torch.cuda.is_available():\n        torch.cuda.empty_cache()\n    print(f\"Using device: {device}\")", "detail": "test_inference", "documentation": {}}, {"label": "test_do_test_function", "kind": 2, "importPath": "test_inference", "description": "test_inference", "peekOfCode": "def test_do_test_function():\n    \"\"\"测试do_test函数\"\"\"\n    print(\"\\nTesting do_test function...\")\n    batch_size = 1  # 减小batch size\n    device = \"cuda:0\" if torch.cuda.is_available() else \"cpu\"\n    # 清理GPU内存\n    if torch.cuda.is_available():\n        torch.cuda.empty_cache()\n    set_seed(1024)\n    testing_loader = make_dataloader(test_dataset_path=\"data/s669.csv\", batch_size=batch_size, is_training=False)", "detail": "test_inference", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_inference", "description": "test_inference", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"DVE-stability Inference Test\")\n    print(\"=\" * 60)\n    success_count = 0\n    total_tests = 2\n    if test_inference():\n        success_count += 1\n    if test_do_test_function():", "detail": "test_inference", "documentation": {}}, {"label": "test_training_step", "kind": 2, "importPath": "test_training", "description": "test_training", "peekOfCode": "def test_training_step():\n    \"\"\"测试训练的一个步骤\"\"\"\n    print(\"Testing training step...\")\n    # 设置随机种子\n    set_seed(1024)\n    # 配置参数\n    lr = 6e-6\n    batch_size = 1\n    device = \"cuda:0\" if torch.cuda.is_available() else \"cpu\"\n    print(f\"Using device: {device}\")", "detail": "test_training", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_training", "description": "test_training", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"DVE-stability Training Test\")\n    print(\"=\" * 60)\n    if test_training_step():\n        print(\"\\n🎉 Training test passed!\")\n        print(\"The model can be trained successfully.\")\n        print(\"\\nTo run full training:\")\n        print(\"python train.py\")", "detail": "test_training", "documentation": {}}, {"label": "seed", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "seed = 1024\nset_seed(seed)\nlr = 6e-6\nepochs = 20\nbatch_size = 1\ndevice = \"cuda:0\"\nload_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model", "detail": "train", "documentation": {}}, {"label": "lr", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "lr = 6e-6\nepochs = 20\nbatch_size = 1\ndevice = \"cuda:0\"\nload_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"<PERSON>\"", "detail": "train", "documentation": {}}, {"label": "epochs", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "epochs = 20\nbatch_size = 1\ndevice = \"cuda:0\"\nload_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"Adam\"\n# --> optimizer and scheduler", "detail": "train", "documentation": {}}, {"label": "batch_size", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "batch_size = 1\ndevice = \"cuda:0\"\nload_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"<PERSON>\"\n# --> optimizer and scheduler\noptimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)", "detail": "train", "documentation": {}}, {"label": "device", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "device = \"cuda:0\"\nload_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"<PERSON>\"\n# --> optimizer and scheduler\noptimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)\nscheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")", "detail": "train", "documentation": {}}, {"label": "load_checkpoint", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "load_checkpoint = False\n# --> data\ntraining_loader, testing_loader = make_dataloader(\"data/S2648.csv\", \"data/s669.csv\", batch_size)\n# --> model\nmodel = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"<PERSON>\"\n# --> optimizer and scheduler\noptimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)\nscheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")\n# --> load", "detail": "train", "documentation": {}}, {"label": "model", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "model = make_model(\"ESM_ensamble_cls_env_attention\")\noptimizer_name = \"<PERSON>\"\n# --> optimizer and scheduler\noptimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)\nscheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")\n# --> load\nif load_checkpoint:\n    checkpoint_path = \"[path of your checkpoint]\"\n    checkpoint = torch.load(checkpoint_path, map_location=\"cpu\")\n    model.load_state_dict(checkpoint, strict=False)", "detail": "train", "documentation": {}}, {"label": "optimizer_name", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "optimizer_name = \"<PERSON>\"\n# --> optimizer and scheduler\noptimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)\nscheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")\n# --> load\nif load_checkpoint:\n    checkpoint_path = \"[path of your checkpoint]\"\n    checkpoint = torch.load(checkpoint_path, map_location=\"cpu\")\n    model.load_state_dict(checkpoint, strict=False)\nmodel.to(device)", "detail": "train", "documentation": {}}, {"label": "optimizer", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "optimizer =  make_optimizer(model, lr, optimizer_name=optimizer_name)\nscheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")\n# --> load\nif load_checkpoint:\n    checkpoint_path = \"[path of your checkpoint]\"\n    checkpoint = torch.load(checkpoint_path, map_location=\"cpu\")\n    model.load_state_dict(checkpoint, strict=False)\nmodel.to(device)\n# --> train\ndo_train(model, ", "detail": "train", "documentation": {}}, {"label": "scheduler", "kind": 5, "importPath": "train", "description": "train", "peekOfCode": "scheduler =  make_scheduler(optimizer, lr, training_loader, epochs, scheduler_name=\"CosineLR\")\n# --> load\nif load_checkpoint:\n    checkpoint_path = \"[path of your checkpoint]\"\n    checkpoint = torch.load(checkpoint_path, map_location=\"cpu\")\n    model.load_state_dict(checkpoint, strict=False)\nmodel.to(device)\n# --> train\ndo_train(model, \n         optimizer, ", "detail": "train", "documentation": {}}]