#!/usr/bin/env python3
"""
测试训练功能的简化脚本
只运行几个batch来验证训练流程是否正常
"""

from datasets import make_dataloader
from model import make_model
from solver import make_optimizer, make_scheduler
from utils.set_seed import set_seed
import torch
import numpy as np

def test_training_step():
    """测试训练的一个步骤"""
    print("Testing training step...")
    
    # 设置随机种子
    set_seed(1024)
    
    # 配置参数
    lr = 6e-6
    batch_size = 1
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {device}")
    
    # 加载数据
    print("Loading data...")
    training_loader, testing_loader = make_dataloader("data/S2648.csv", "data/s669.csv", batch_size)
    print(f"Training batches: {len(training_loader)}")
    print(f"Testing batches: {len(testing_loader)}")
    
    # 创建模型
    print("Creating model...")
    model = make_model("ESM_ensamble_cls_env_attention")
    model.to(device)
    
    # 创建优化器
    optimizer = make_optimizer(model, lr, optimizer_name="Adam")
    scheduler = make_scheduler(optimizer, lr, training_loader, 1, scheduler_name="CosineLR")
    
    # 测试一个训练batch
    print("Testing training batch...")
    model.train()
    
    for idx, batch in enumerate(training_loader):
        if idx >= 3:  # 只测试前3个batch
            break
            
        print(f"Processing batch {idx + 1}...")
        
        try:
            input_ids1, input_ids2, pos, labels, classes = batch
            print(f"  Batch shapes: input_ids1={input_ids1.shape}, input_ids2={input_ids2.shape}, pos={pos.shape}, labels={labels.shape}")
            
            input_ids1 = input_ids1.to(device)
            input_ids2 = input_ids2.to(device)
            labels = labels.to(device).reshape(-1, 1)
            
            # 前向传播
            optimizer.zero_grad()
            logitis_output = model(input_ids1, input_ids2, pos, is_training=True)
            
            if len(logitis_output) == 3:
                logits_1, logits_2, logits_mean = logitis_output[0], logitis_output[1], logitis_output[2]
                loss = torch.nn.functional.mse_loss(logits_1, labels) + \
                       torch.nn.functional.mse_loss(logits_2, labels) + \
                       torch.nn.functional.mse_loss(logits_mean, labels)
            else:
                logits_pred = logitis_output[0]
                loss = torch.nn.functional.mse_loss(logits_pred, labels)
            
            print(f"  Loss: {loss.item():.6f}")
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(parameters=model.parameters(), max_norm=0.1)
            optimizer.step()
            
            print(f"  ✓ Batch {idx + 1} processed successfully")
            
        except Exception as e:
            print(f"  ✗ Error in batch {idx + 1}: {e}")
            return False
    
    # 测试一个验证batch
    print("Testing validation batch...")
    model.eval()
    
    with torch.no_grad():
        for idx, batch in enumerate(testing_loader):
            if idx >= 1:  # 只测试1个batch
                break
                
            try:
                input_ids1, input_ids2, pos, labels, classes = batch
                input_ids1 = input_ids1.to(device)
                input_ids2 = input_ids2.to(device)
                labels = labels.to(device).reshape(-1, 1)
                
                logits = model(input_ids1, input_ids2, pos)
                loss = torch.nn.functional.mse_loss(logits, labels)
                
                print(f"  Validation loss: {loss.item():.6f}")
                print(f"  Predictions: {logits.flatten()}")
                print(f"  Labels: {labels.flatten()}")
                print(f"  ✓ Validation batch processed successfully")
                
            except Exception as e:
                print(f"  ✗ Error in validation: {e}")
                return False
    
    print("✓ Training step test completed successfully!")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("DVE-stability Training Test")
    print("=" * 60)
    
    if test_training_step():
        print("\n🎉 Training test passed!")
        print("The model can be trained successfully.")
        print("\nTo run full training:")
        print("python train.py")
    else:
        print("\n❌ Training test failed!")
        print("Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
