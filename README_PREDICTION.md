# DVE-stability 蛋白质突变预测工具

## 🎯 项目概述

DVE-stability是一个基于双视角集成学习的蛋白质稳定性变化预测框架，能够从单一蛋白质序列预测突变引起的稳定性变化（ΔΔG值）。

## 📁 文件说明

### 核心预测脚本
- **`predict_mutation.py`** - 主要预测脚本，支持单个和批量预测
- **`quick_predict.py`** - 交互式快速预测工具
- **`MUTATION_PREDICTION_GUIDE.md`** - 详细使用指南

### 示例文件
- **`example_mutations.csv`** - 批量预测示例输入文件
- **`example_mutations_predictions.csv`** - 预测结果示例

### 环境配置
- **`test_environment.py`** - 环境测试脚本
- **`test_training.py`** - 训练功能测试
- **`test_inference.py`** - 推理功能测试

## 🚀 快速开始

### 1. 环境激活
```bash
conda activate dvestability
```

### 2. 快速预测（交互式）
```bash
python quick_predict.py
```

### 3. 单个突变预测
```bash
python predict_mutation.py \
    --wild_seq "YOUR_PROTEIN_SEQUENCE" \
    --mutation "D23A" \
    --position 22
```

### 4. 批量预测
```bash
python predict_mutation.py --input_file example_mutations.csv
```

### 5. 演示模式
```bash
python quick_predict.py demo
```

## 📊 输入输出格式

### 输入要求
- **蛋白质序列**: 标准单字母氨基酸代码
- **突变格式**: `原氨基酸+位置+突变氨基酸` (如: D23A)
- **位置编号**: 0-based索引 (第一个氨基酸位置为0)

### 输出解释
- **ΔΔG > 0**: 去稳定化突变（稳定性降低）
- **ΔΔG < 0**: 稳定化突变（稳定性增加）
- **ΔΔG ≈ 0**: 中性突变（影响很小）

## 🔧 工具特性

### predict_mutation.py
- ✅ 单个突变预测
- ✅ 批量预测
- ✅ 支持预训练模型
- ✅ GPU/CPU自动选择
- ✅ 详细统计信息

### quick_predict.py
- ✅ 交互式界面
- ✅ 实时输入验证
- ✅ 演示模式
- ✅ 用户友好提示

## 📈 使用示例

### 示例1: 单点突变预测
```bash
# 预测D23A突变对蛋白质稳定性的影响
python predict_mutation.py \
    --wild_seq "MKVIFLKDVKGKGKKGEIKNVADGYANNFLFKQGLAIEATPANLKALEAQKQKEQRQAAEELANAKKLKEQLEKLTVTIPAKAGEGGRLFGSITSKQIAESLQAQHGLKLDKRKIELADAIRALGYTNVPVKLHPEVTATLKVHVTEQK" \
    --mutation "D23A" \
    --position 22

# 输出:
# 🎯 Prediction Result:
#    Mutation: D23A
#    Position: 22
#    ΔΔG:      0.010
#    Effect:   Destabilizing (stability decreased)
```

### 示例2: 批量预测
```bash
# 从CSV文件批量预测多个突变
python predict_mutation.py --input_file example_mutations.csv

# 输出:
# ✓ 1/5: D23A at position 22 -> ΔΔG = 0.010
# ✓ 2/5: E100A at position 99 -> ΔΔG = -0.008
# 📈 Prediction Statistics:
#    Mean ΔΔG: 0.004
#    Range:    [-0.008, 0.010]
```

## ⚠️ 重要说明

### 模型状态
- 当前使用**随机初始化模型**，结果仅供测试参考
- 如需准确预测，请提供训练好的checkpoint文件
- 使用 `--checkpoint path/to/model.pt` 参数加载预训练模型

### 技术限制
- 序列长度限制：最大1022个氨基酸
- 仅支持单点突变预测
- 需要CUDA支持以获得最佳性能

### 结果解释
- ΔΔG值的准确性依赖于模型训练质量
- 建议与实验数据对比验证
- 对于关键应用，推荐进行实验验证

## 🛠️ 故障排除

### 常见问题
1. **CUDA内存不足**: 使用 `--device cpu` 参数
2. **序列过长**: 自动截断到1022个氨基酸
3. **突变格式错误**: 确保格式为 "D23A"
4. **位置超出范围**: 检查位置是否在序列范围内

### 获取帮助
```bash
python predict_mutation.py --help
python quick_predict.py --help
```

## 📚 相关文档

- **详细使用指南**: `MUTATION_PREDICTION_GUIDE.md`
- **项目主README**: `README.md`
- **原始论文**: "Predicting protein stability changes upon mutations with dual-view ensemble learning from single sequence"

## 🔬 应用场景

1. **蛋白质工程**: 设计更稳定的蛋白质变体
2. **疾病研究**: 分析致病性突变的影响
3. **进化分析**: 研究突变的进化压力
4. **药物开发**: 优化蛋白质药物的稳定性

## 📞 技术支持

如遇到问题，请检查：
1. 环境是否正确配置
2. 输入格式是否正确
3. 设备内存是否充足
4. 依赖包是否完整安装

---

**注意**: 本工具基于DVE-stability框架开发，用于学术研究和教育目的。商业使用请遵循相应许可协议。
