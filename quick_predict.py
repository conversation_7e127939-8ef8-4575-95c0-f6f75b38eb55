#!/usr/bin/env python3
"""
DVE-stability 快速突变预测脚本

简化版本，用于快速预测单个突变的ΔΔG值

使用示例:
python quick_predict.py
然后按提示输入序列和突变信息
"""

import torch
import esm
from model import make_model
from utils.set_seed import set_seed
import warnings
warnings.filterwarnings('ignore')

def quick_predict():
    """快速预测函数"""
    print("=" * 60)
    print("DVE-stability 快速突变预测")
    print("=" * 60)
    
    # 设置设备
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 初始化模型
    print("正在加载模型...")
    set_seed(1024)
    
    # 初始化ESM
    _, esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()
    tokenizer = esm_alphabet.get_batch_converter()
    
    # 初始化DVE-stability模型
    model = make_model("ESM_ensamble_cls_env_attention")
    model.to(device)
    model.eval()
    
    print("⚠ 注意: 使用随机初始化模型，结果仅供参考")
    print("如需准确结果，请使用训练好的checkpoint")
    print()
    
    while True:
        try:
            # 获取用户输入
            print("请输入蛋白质信息:")
            wild_seq = input("野生型序列: ").strip().upper()
            
            if not wild_seq:
                print("序列不能为空！")
                continue
            
            print(f"序列长度: {len(wild_seq)}")
            if len(wild_seq) > 1022:
                print("⚠ 警告: 序列过长，将截断到1022个氨基酸")
                wild_seq = wild_seq[:1022]
            
            # 获取突变信息
            mutation = input("突变信息 (如 D23A): ").strip().upper()
            if len(mutation) < 3:
                print("突变格式错误！请使用格式如 D23A")
                continue
            
            # 解析突变
            original_aa = mutation[0]
            mutant_aa = mutation[-1]
            
            # 获取位置
            try:
                position_str = input("突变位置 (0-based, 如第23位输入22): ").strip()
                position = int(position_str)
            except ValueError:
                print("位置必须是数字！")
                continue
            
            if position >= len(wild_seq):
                print(f"位置超出序列范围！序列长度为{len(wild_seq)}")
                continue
            
            # 验证原始氨基酸
            if wild_seq[position] != original_aa:
                print(f"⚠ 警告: 位置{position}的氨基酸是{wild_seq[position]}，不是{original_aa}")
                confirm = input("是否继续？(y/n): ").strip().lower()
                if confirm != 'y':
                    continue
            
            # 创建突变序列
            mutant_seq = list(wild_seq)
            mutant_seq[position] = mutant_aa
            mutant_seq = ''.join(mutant_seq)
            
            print(f"\n正在预测突变 {original_aa}{position+1}{mutant_aa}...")
            
            # Tokenize
            _, _, wild_tokens = tokenizer([("", wild_seq)])
            _, _, mutant_tokens = tokenizer([("", mutant_seq)])
            
            # 移动到设备
            wild_tokens = wild_tokens.to(device)
            mutant_tokens = mutant_tokens.to(device)
            pos_tensor = torch.tensor([position + 1], dtype=torch.long)
            
            # 预测
            with torch.no_grad():
                prediction = model(wild_tokens, mutant_tokens, pos_tensor, is_training=False)
                ddg_value = prediction.cpu().numpy().flatten()[0]
            
            # 显示结果
            print("\n" + "="*40)
            print("🎯 预测结果:")
            print(f"   突变: {original_aa}{position+1}{mutant_aa}")
            print(f"   位置: {position} (0-based)")
            print(f"   ΔΔG:  {ddg_value:.3f}")
            
            if ddg_value > 0.5:
                effect = "显著去稳定化 (稳定性明显降低)"
            elif ddg_value > 0:
                effect = "轻微去稳定化 (稳定性略微降低)"
            elif ddg_value > -0.5:
                effect = "轻微稳定化 (稳定性略微增加)"
            else:
                effect = "显著稳定化 (稳定性明显增加)"
            
            print(f"   效果: {effect}")
            print("="*40)
            
            # 询问是否继续
            print("\n是否继续预测其他突变？")
            continue_pred = input("输入 'y' 继续，其他键退出: ").strip().lower()
            if continue_pred != 'y':
                break
            
            print("\n" + "-"*60 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")
            print("请重新输入")
            continue
    
    print("\n感谢使用 DVE-stability 突变预测工具！")

def demo_mode():
    """演示模式"""
    print("=" * 60)
    print("DVE-stability 演示模式")
    print("=" * 60)
    
    # 示例数据
    examples = [
        {
            "name": "示例1: 稳定化突变",
            "seq": "MKVIFLKDVKGKGKKGEIKNVADGYANNFLFKQGLAIEATPANLKALEAQKQKEQRQAAEELANAKKLKEQLEKLTVTIPAKAGEGGRLFGSITSKQIAESLQAQHGLKLDKRKIELADAIRALGYTNVPVKLHPEVTATLKVHVTEQK",
            "mutation": "D23A",
            "position": 22
        },
        {
            "name": "示例2: 去稳定化突变", 
            "seq": "QTDVIAQRKAILKQMGEATKPIAAMLKGEAKFDQAVVQKSLAAIADDSKKLPALFPADSKTGGDTAALPKIWEDKAKFDDLFAKLAAAATAAQGTIKDEASLKANIGGVLGNCKSCHDDFRAKKS",
            "mutation": "A104H",
            "position": 103
        }
    ]
    
    device = "cpu"  # 演示模式使用CPU
    print(f"使用设备: {device}")
    
    # 初始化模型
    print("正在加载模型...")
    set_seed(1024)
    _, esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()
    tokenizer = esm_alphabet.get_batch_converter()
    model = make_model("ESM_ensamble_cls_env_attention")
    model.to(device)
    model.eval()
    
    print("⚠ 注意: 使用随机初始化模型，结果仅供参考\n")
    
    for i, example in enumerate(examples, 1):
        print(f"🧬 {example['name']}")
        print(f"   序列长度: {len(example['seq'])}")
        print(f"   突变: {example['mutation']}")
        print(f"   位置: {example['position']}")
        
        # 创建突变序列
        wild_seq = example['seq']
        position = example['position']
        mutation = example['mutation']
        
        mutant_seq = list(wild_seq)
        mutant_seq[position] = mutation[-1]
        mutant_seq = ''.join(mutant_seq)
        
        # 预测
        _, _, wild_tokens = tokenizer([("", wild_seq)])
        _, _, mutant_tokens = tokenizer([("", mutant_seq)])
        
        wild_tokens = wild_tokens.to(device)
        mutant_tokens = mutant_tokens.to(device)
        pos_tensor = torch.tensor([position + 1], dtype=torch.long)
        
        with torch.no_grad():
            prediction = model(wild_tokens, mutant_tokens, pos_tensor, is_training=False)
            ddg_value = prediction.cpu().numpy().flatten()[0]
        
        print(f"   ΔΔG: {ddg_value:.3f}")
        if ddg_value > 0:
            print(f"   效果: 去稳定化 (稳定性降低)")
        else:
            print(f"   效果: 稳定化 (稳定性增加)")
        
        if i < len(examples):
            print()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_mode()
    else:
        quick_predict()
