#!/usr/bin/env python3
"""
测试环境配置脚本
验证所有依赖是否正确安装，模型是否可以正常初始化
"""

import torch
import numpy as np
import pandas as pd
from datasets import make_dataloader
from model import make_model
from utils.set_seed import set_seed

def test_imports():
    """测试所有必要的包是否可以正常导入"""
    print("Testing imports...")
    try:
        import esm
        print("✓ ESM imported successfully")
        
        import matplotlib.pyplot as plt
        print("✓ Matplotlib imported successfully")
        
        import sklearn
        print("✓ Scikit-learn imported successfully")
        
        import scipy
        print("✓ Scipy imported successfully")
        
        print("✓ All imports successful!")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_data_loading():
    """测试数据加载功能"""
    print("\nTesting data loading...")
    try:
        # 检查数据文件是否存在
        import os
        if not os.path.exists("data/s669.csv"):
            print("✗ Test data file not found: data/s669.csv")
            return False
        
        # 尝试加载数据
        testing_loader = make_dataloader(test_dataset_path="data/s669.csv", batch_size=2, is_training=False)
        print(f"✓ Data loader created successfully")
        
        # 尝试获取一个batch
        for batch in testing_loader:
            print(f"✓ Successfully loaded batch with {len(batch)} items")
            break
        
        return True
    except Exception as e:
        print(f"✗ Data loading error: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\nTesting model creation...")
    try:
        set_seed(1024)
        model = make_model("ESM_ensamble_cls_env_attention")
        print("✓ Model created successfully")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"✓ Total parameters: {total_params:,}")
        print(f"✓ Trainable parameters: {trainable_params:,}")
        
        return True, model
    except Exception as e:
        print(f"✗ Model creation error: {e}")
        return False, None

def test_model_forward():
    """测试模型前向传播（使用虚拟数据）"""
    print("\nTesting model forward pass...")
    try:
        set_seed(1024)
        model = make_model("ESM_ensamble_cls_env_attention")
        model.eval()
        
        # 创建虚拟输入数据
        batch_size = 2
        seq_length = 50
        
        # 创建随机token ids (ESM的词汇表大小约为33)
        token_ids1 = torch.randint(0, 33, (batch_size, seq_length))
        token_ids2 = torch.randint(0, 33, (batch_size, seq_length))
        pos = torch.randint(1, seq_length-1, (batch_size,))  # 突变位置
        
        print(f"✓ Created dummy input: batch_size={batch_size}, seq_length={seq_length}")
        
        # 前向传播
        with torch.no_grad():
            output = model(token_ids1, token_ids2, pos, is_training=False)
        
        print(f"✓ Forward pass successful, output shape: {output.shape}")
        print(f"✓ Output values: {output.flatten()}")
        
        return True
    except Exception as e:
        print(f"✗ Model forward pass error: {e}")
        return False

def test_cuda_availability():
    """测试CUDA是否可用"""
    print("\nTesting CUDA availability...")
    if torch.cuda.is_available():
        print(f"✓ CUDA is available")
        print(f"✓ CUDA device count: {torch.cuda.device_count()}")
        print(f"✓ Current CUDA device: {torch.cuda.current_device()}")
        print(f"✓ CUDA device name: {torch.cuda.get_device_name()}")
        return True
    else:
        print("⚠ CUDA is not available, will use CPU")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("DVE-stability Environment Test")
    print("=" * 60)
    
    # 运行所有测试
    tests_passed = 0
    total_tests = 5
    
    if test_imports():
        tests_passed += 1
    
    if test_cuda_availability():
        tests_passed += 1
    
    if test_data_loading():
        tests_passed += 1
    
    if test_model_creation()[0]:
        tests_passed += 1
    
    if test_model_forward():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Environment is ready.")
        print("\nNext steps:")
        print("1. To train the model: python train.py")
        print("2. To test with a checkpoint: modify test.py with your checkpoint path")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
