#!/usr/bin/env python3
"""
测试推理功能的脚本
使用随机初始化的模型进行推理测试
"""

import torch
from datasets import make_dataloader
from model import make_model
from processor import do_test
from utils.set_seed import set_seed
import numpy as np

def test_inference():
    """测试推理功能"""
    print("Testing inference...")
    
    # 设置参数
    batch_size = 1  # 减小batch size以节省GPU内存
    device = "cuda:0" if torch.cuda.is_available() else "cpu"

    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    print(f"Using device: {device}")
    
    # 设置随机种子
    set_seed(1024)
    
    # 加载测试数据
    print("Loading test data...")
    testing_loader = make_dataloader(test_dataset_path="data/s669.csv", batch_size=batch_size, is_training=False)
    print(f"Test batches: {len(testing_loader)}")
    
    # 创建模型（随机初始化）
    print("Creating model...")
    model = make_model("ESM_ensamble_cls_env_attention")
    model.to(device)
    
    print("Running inference test...")
    
    # 手动运行几个batch的推理
    model.eval()
    predictions = []
    labels = []
    
    with torch.no_grad():
        for idx, batch in enumerate(testing_loader):
            if idx >= 5:  # 只测试前5个batch
                break
                
            try:
                input_ids1, input_ids2, pos, batch_labels, classes = batch
                input_ids1 = input_ids1.to(device)
                input_ids2 = input_ids2.to(device)
                
                # 推理
                logits = model(input_ids1, input_ids2, pos, is_training=False)
                
                predictions.extend(logits.cpu().numpy().flatten())
                labels.extend(batch_labels.numpy().flatten())
                
                print(f"  Batch {idx + 1}: predictions shape={logits.shape}, range=[{logits.min().item():.3f}, {logits.max().item():.3f}]")
                
            except Exception as e:
                print(f"  ✗ Error in batch {idx + 1}: {e}")
                return False
    
    predictions = np.array(predictions)
    labels = np.array(labels)
    
    print(f"\nInference Results:")
    print(f"  Total samples: {len(predictions)}")
    print(f"  Predictions range: [{predictions.min():.3f}, {predictions.max():.3f}]")
    print(f"  Labels range: [{labels.min():.3f}, {labels.max():.3f}]")
    print(f"  Mean prediction: {predictions.mean():.3f}")
    print(f"  Mean label: {labels.mean():.3f}")
    
    # 计算一些基本指标
    mse = np.mean((predictions - labels) ** 2)
    mae = np.mean(np.abs(predictions - labels))
    
    print(f"  MSE: {mse:.3f}")
    print(f"  MAE: {mae:.3f}")
    print(f"  RMSE: {np.sqrt(mse):.3f}")
    
    print("✓ Inference test completed successfully!")
    return True

def test_do_test_function():
    """测试do_test函数"""
    print("\nTesting do_test function...")
    
    batch_size = 1  # 减小batch size
    device = "cuda:0" if torch.cuda.is_available() else "cpu"

    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    set_seed(1024)
    testing_loader = make_dataloader(test_dataset_path="data/s669.csv", batch_size=batch_size, is_training=False)
    model = make_model("ESM_ensamble_cls_env_attention")
    model.to(device)
    
    try:
        # 注意：这里我们不加载checkpoint，使用随机初始化的模型
        print("Running do_test with random initialized model...")
        do_test(model, testing_loader, device)
        print("✓ do_test function works correctly!")
        return True
    except Exception as e:
        print(f"✗ Error in do_test: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("DVE-stability Inference Test")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    if test_inference():
        success_count += 1
    
    if test_do_test_function():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All inference tests passed!")
        print("\nThe model can perform inference successfully.")
        print("Note: Results are from randomly initialized model.")
        print("For meaningful results, train the model first or load a checkpoint.")
    else:
        print("❌ Some inference tests failed!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
