#!/usr/bin/env python3
"""
DVE-stability 蛋白质突变预测脚本

使用方法:
1. 单个突变预测:
   python predict_mutation.py --wild_seq "MKVIFLKDVK..." --mutation "D23A" --position 22

2. 批量预测:
   python predict_mutation.py --input_file mutations.csv

输入格式说明:
- wild_seq: 野生型蛋白质序列
- mutation: 突变信息，格式为 "原氨基酸+位置+突变氨基酸"，如 "D23A"
- position: 突变位置（0-based索引）

输出:
- ΔΔG值: 正值表示稳定性降低，负值表示稳定性增加
"""

import torch
import pandas as pd
import numpy as np
import argparse
import esm
from model import make_model
from utils.set_seed import set_seed
import warnings
warnings.filterwarnings('ignore')

class MutationPredictor:
    def __init__(self, checkpoint_path=None, device="cuda:0"):
        """
        初始化突变预测器
        
        Args:
            checkpoint_path: 预训练模型路径，如果为None则使用随机初始化模型
            device: 计算设备
        """
        self.device = device if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # 设置随机种子
        set_seed(1024)
        
        # 初始化ESM模型和tokenizer
        _, self.esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()
        self.tokenizer = self.esm_alphabet.get_batch_converter()
        
        # 初始化DVE-stability模型
        self.model = make_model("ESM_ensamble_cls_env_attention")
        
        # 加载预训练权重
        if checkpoint_path:
            try:
                checkpoint = torch.load(checkpoint_path, map_location="cpu")
                self.model.load_state_dict(checkpoint)
                print(f"✓ Loaded checkpoint from {checkpoint_path}")
            except Exception as e:
                print(f"⚠ Warning: Could not load checkpoint ({e})")
                print("Using randomly initialized model (results may not be meaningful)")
        else:
            print("⚠ Warning: Using randomly initialized model")
            print("For meaningful results, please provide a trained checkpoint")
        
        self.model.to(self.device)
        self.model.eval()
    
    def create_mutant_sequence(self, wild_seq, mutation, position):
        """
        根据突变信息创建突变序列
        
        Args:
            wild_seq: 野生型序列
            mutation: 突变信息，如 "D23A"
            position: 突变位置（0-based）
        
        Returns:
            mutant_seq: 突变序列
        """
        # 解析突变信息
        if len(mutation) >= 3:
            original_aa = mutation[0]
            mutant_aa = mutation[-1]
        else:
            raise ValueError(f"Invalid mutation format: {mutation}")
        
        # 验证原始氨基酸
        if position >= len(wild_seq):
            raise ValueError(f"Position {position} is out of range for sequence length {len(wild_seq)}")
        
        if wild_seq[position] != original_aa:
            print(f"⚠ Warning: Expected {original_aa} at position {position}, but found {wild_seq[position]}")
        
        # 创建突变序列
        mutant_seq = list(wild_seq)
        mutant_seq[position] = mutant_aa
        
        return ''.join(mutant_seq)
    
    def predict_single_mutation(self, wild_seq, mutation, position):
        """
        预测单个突变的ΔΔG值
        
        Args:
            wild_seq: 野生型序列
            mutation: 突变信息
            position: 突变位置（0-based）
        
        Returns:
            ddg_prediction: 预测的ΔΔG值
        """
        # 创建突变序列
        mutant_seq = self.create_mutant_sequence(wild_seq, mutation, position)
        
        # 截断序列（ESM模型限制）
        wild_seq_truncated = wild_seq[:1022]
        mutant_seq_truncated = mutant_seq[:1022]
        
        # Tokenize序列
        _, _, wild_tokens = self.tokenizer([("", wild_seq_truncated)])
        _, _, mutant_tokens = self.tokenizer([("", mutant_seq_truncated)])
        
        # 转换为tensor并移动到设备
        wild_tokens = wild_tokens.to(self.device)
        mutant_tokens = mutant_tokens.to(self.device)
        pos_tensor = torch.tensor([position + 1], dtype=torch.long)  # +1 for ESM CLS token
        
        # 预测
        with torch.no_grad():
            prediction = self.model(wild_tokens, mutant_tokens, pos_tensor, is_training=False)
            ddg_value = prediction.cpu().numpy().flatten()[0]
        
        return ddg_value
    
    def predict_from_dataframe(self, df):
        """
        从DataFrame批量预测
        
        Args:
            df: 包含 'wild_seq', 'mutation', 'position' 列的DataFrame
        
        Returns:
            predictions: 预测结果列表
        """
        predictions = []
        
        for idx, row in df.iterrows():
            try:
                wild_seq = row['wild_seq']
                mutation = row['mutation']
                position = int(row['position'])
                
                ddg_pred = self.predict_single_mutation(wild_seq, mutation, position)
                predictions.append(ddg_pred)
                
                print(f"✓ {idx+1}/{len(df)}: {mutation} at position {position} -> ΔΔG = {ddg_pred:.3f}")
                
            except Exception as e:
                print(f"✗ Error processing row {idx}: {e}")
                predictions.append(np.nan)
        
        return predictions

def main():
    parser = argparse.ArgumentParser(description='DVE-stability Mutation Prediction')
    parser.add_argument('--wild_seq', type=str, help='Wild-type protein sequence')
    parser.add_argument('--mutation', type=str, help='Mutation in format "D23A"')
    parser.add_argument('--position', type=int, help='Mutation position (0-based)')
    parser.add_argument('--input_file', type=str, help='CSV file with mutations to predict')
    parser.add_argument('--output_file', type=str, help='Output CSV file for batch predictions')
    parser.add_argument('--checkpoint', type=str, help='Path to trained model checkpoint')
    parser.add_argument('--device', type=str, default='cuda:0', help='Device to use')
    
    args = parser.parse_args()
    
    # 初始化预测器
    predictor = MutationPredictor(checkpoint_path=args.checkpoint, device=args.device)
    
    if args.input_file:
        # 批量预测模式
        print(f"\n📁 Loading mutations from {args.input_file}")
        df = pd.read_csv(args.input_file)
        
        required_columns = ['wild_seq', 'mutation', 'position']
        if not all(col in df.columns for col in required_columns):
            print(f"❌ Error: Input file must contain columns: {required_columns}")
            return
        
        print(f"📊 Predicting {len(df)} mutations...")
        predictions = predictor.predict_from_dataframe(df)
        
        # 保存结果
        df['predicted_ddg'] = predictions
        output_file = args.output_file or args.input_file.replace('.csv', '_predictions.csv')
        df.to_csv(output_file, index=False)
        print(f"💾 Results saved to {output_file}")
        
        # 显示统计信息
        valid_predictions = [p for p in predictions if not np.isnan(p)]
        if valid_predictions:
            print(f"\n📈 Prediction Statistics:")
            print(f"   Mean ΔΔG: {np.mean(valid_predictions):.3f}")
            print(f"   Std ΔΔG:  {np.std(valid_predictions):.3f}")
            print(f"   Range:    [{np.min(valid_predictions):.3f}, {np.max(valid_predictions):.3f}]")
    
    elif args.wild_seq and args.mutation and args.position is not None:
        # 单个预测模式
        print(f"\n🧬 Predicting mutation: {args.mutation} at position {args.position}")
        print(f"   Wild-type sequence length: {len(args.wild_seq)}")
        
        try:
            ddg_prediction = predictor.predict_single_mutation(
                args.wild_seq, args.mutation, args.position
            )
            
            print(f"\n🎯 Prediction Result:")
            print(f"   Mutation: {args.mutation}")
            print(f"   Position: {args.position}")
            print(f"   ΔΔG:      {ddg_prediction:.3f}")
            
            if ddg_prediction > 0:
                print(f"   Effect:   Destabilizing (stability decreased)")
            else:
                print(f"   Effect:   Stabilizing (stability increased)")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    else:
        print("❌ Error: Please provide either:")
        print("   1. --wild_seq, --mutation, and --position for single prediction")
        print("   2. --input_file for batch prediction")
        print("\nExample usage:")
        print('   python predict_mutation.py --wild_seq "MKVIFLKDVK..." --mutation "D23A" --position 22')
        print('   python predict_mutation.py --input_file mutations.csv')

if __name__ == "__main__":
    main()
