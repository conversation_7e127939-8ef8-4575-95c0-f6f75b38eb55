# DVE-stability 蛋白质突变预测指南

## 概述

DVE-stability是一个基于双视角集成学习的蛋白质稳定性变化预测框架，可以从单一序列预测突变引起的蛋白质稳定性变化（ΔΔG值）。

## 预测原理

- **输入**: 野生型蛋白质序列 + 突变信息
- **输出**: ΔΔG值（稳定性变化）
  - **正值**: 突变导致蛋白质稳定性降低（去稳定化）
  - **负值**: 突变导致蛋白质稳定性增加（稳定化）
  - **接近0**: 突变对稳定性影响很小

## 使用方法

### 1. 环境准备

确保已经配置好dvestability conda环境：

```bash
conda activate dvestability
```

### 2. 单个突变预测

```bash
python predict_mutation.py \
    --wild_seq "MKVIFLKDVKGKGKKGEIKNVADGYANNFLFKQGLAIEATPANLKALEAQKQKEQRQAAEELANAKKLKEQLEKLTVTIPAKAGEGGRLFGSITSKQIAESLQAQHGLKLDKRKIELADAIRALGYTNVPVKLHPEVTATLKVHVTEQK" \
    --mutation "D23A" \
    --position 22
```

**参数说明:**
- `--wild_seq`: 野生型蛋白质序列（单字母氨基酸代码）
- `--mutation`: 突变信息，格式为"原氨基酸+位置+突变氨基酸"
- `--position`: 突变位置（0-based索引，即第一个氨基酸位置为0）

### 3. 批量预测

#### 3.1 准备输入文件

创建CSV文件，包含以下列：
- `wild_seq`: 野生型序列
- `mutation`: 突变信息
- `position`: 突变位置（0-based）

示例文件 `mutations.csv`:
```csv
wild_seq,mutation,position
MKVIFLKDVK...,D23A,22
MKVIFLKDVK...,E100A,99
```

#### 3.2 运行批量预测

```bash
python predict_mutation.py --input_file example_mutations.csv
```

结果将保存到 `example_mutations_predictions.csv`

### 4. 使用预训练模型

如果您有训练好的模型checkpoint：

```bash
python predict_mutation.py \
    --wild_seq "YOUR_SEQUENCE" \
    --mutation "D23A" \
    --position 22 \
    --checkpoint "path/to/your/checkpoint.pt"
```

## 输入格式要求

### 蛋白质序列
- 使用标准的单字母氨基酸代码
- 支持的氨基酸：A, R, N, D, C, Q, E, G, H, I, L, K, M, F, P, S, T, W, Y, V
- 最大长度：1022个氨基酸（受ESM模型限制）

### 突变信息
- 格式：`原氨基酸 + 位置 + 突变氨基酸`
- 示例：
  - `D23A`: 第23位的天冬氨酸(D)突变为丙氨酸(A)
  - `K100R`: 第100位的赖氨酸(K)突变为精氨酸(R)
  - `F50W`: 第50位的苯丙氨酸(F)突变为色氨酸(W)

### 位置编号
- 使用0-based索引
- 第一个氨基酸的位置为0
- 第二个氨基酸的位置为1
- 以此类推...

## 结果解释

### ΔΔG值含义
- **ΔΔG > 0**: 去稳定化突变
  - 值越大，稳定性损失越严重
  - 通常 ΔΔG > 2.0 被认为是显著的去稳定化
- **ΔΔG < 0**: 稳定化突变
  - 值越小（绝对值越大），稳定性增加越多
  - 稳定化突变相对较少见
- **ΔΔG ≈ 0**: 中性突变，对稳定性影响很小

### 实际应用建议
1. **蛋白质工程**: 选择ΔΔG < 0的突变来提高蛋白质稳定性
2. **疾病研究**: ΔΔG > 2.0的突变可能与疾病相关
3. **进化分析**: 中性突变（|ΔΔG| < 1.0）在进化中更容易被保留

## 示例运行

### 示例1：单个预测
```bash
python predict_mutation.py \
    --wild_seq "MKVIFLKDVKGKGKKGEIKNVADGYANNFLFKQGLAIEATPANLKALEAQKQKEQRQAAEELANAKKLKEQLEKLTVTIPAKAGEGGRLFGSITSKQIAESLQAQHGLKLDKRKIELADAIRALGYTNVPVKLHPEVTATLKVHVTEQK" \
    --mutation "D23A" \
    --position 22

# 输出示例:
# 🧬 Predicting mutation: D23A at position 22
# 🎯 Prediction Result:
#    Mutation: D23A
#    Position: 22
#    ΔΔG:      -0.460
#    Effect:   Stabilizing (stability increased)
```

### 示例2：批量预测
```bash
python predict_mutation.py --input_file example_mutations.csv

# 输出示例:
# ✓ 1/5: D23A at position 22 -> ΔΔG = -0.460
# ✓ 2/5: E100A at position 99 -> ΔΔG = -0.980
# ✓ 3/5: L133A at position 132 -> ΔΔG = -2.940
# 📈 Prediction Statistics:
#    Mean ΔΔG: -1.460
#    Std ΔΔG:  1.240
#    Range:    [-2.940, -0.460]
```

## 注意事项

1. **模型限制**: 
   - 序列长度不能超过1022个氨基酸
   - 只支持单点突变预测

2. **预训练模型**: 
   - 没有预训练模型时使用随机初始化，结果仅供测试
   - 建议使用训练好的checkpoint获得有意义的结果

3. **计算资源**: 
   - 推荐使用GPU加速
   - 大批量预测可能需要较长时间

4. **结果验证**: 
   - 建议与实验数据对比验证
   - 对于关键应用，建议进行实验验证

## 故障排除

### 常见错误
1. **CUDA内存不足**: 减小batch size或使用CPU
2. **序列过长**: 截断到1022个氨基酸
3. **突变格式错误**: 检查突变信息格式
4. **位置超出范围**: 确认位置索引正确

### 获取帮助
```bash
python predict_mutation.py --help
```
